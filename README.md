# 📚 StudyBlog

Uma plataforma moderna para compartilhar conhecimento e experiências de estudo. Desenvolvida com Next.js, TypeScript e Tailwind CSS, oferece uma experiência completa para criar, gerenciar e consumir conteúdo educacional, ainda em criação...

## ⚠️ Status do Projeto

**Este projeto está em fase de desenvolvimento e aprendizado!**

- 🎓 **Frontend**: Em desenvolvimento ativo
- 🔧 **Backend**: Também sendo criado durante meus estudos
- ⏳ **Tempo estimado**: Pode demorar um pouco até conseguir criar um backend totalmente funcional
- 📚 **Objetivo**: Aprender e praticar desenvolvimento full-stack

_Estou estudando e desenvolvendo este projeto como forma de aprendizado. O backend está sendo criado paralelamente, então algumas funcionalidades podem não estar totalmente operacionais ainda._

## 🌐 Variáveis de Ambiente Importantes

Adicione a seguinte variável ao seu arquivo .env.local:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
```

- `NEXT_PUBLIC_API_URL`: URL da API (NestJS)

Certifique-se de configurar corretamente essa variável para o ambiente de desenvolvimento e produção.

## 🚀 Como Executar

### Desenvolvimento

```bash
npm run dev
# ou
yarn dev
```

> Certifique-se de que a variável NEXT_PUBLIC_API_URL esteja corretamente configurada no seu .env.local antes de iniciar o frontend.

## ✨ O que você pode fazer

### 🔐 **Conta e Acesso**

- Crie sua conta e verifique seu email
- Faça login com email e senha
- Recupere sua senha se esquecer
- Gerencie seu perfil com foto e bio
- Adicione links das suas redes sociais

### 📝 **Conteúdo**

- Veja posts recentes na página inicial
- Explore todos os posts com busca e filtros
- Leia posts completos com imagens
- Crie e edite posts (se for admin)
- Faça upload de imagens para seus posts

### 👥 **Gestão (para admins)**

- Painel completo de gerenciamento de usuários
- Promova usuários para admin
- Busque usuários por nome ou email
- Gerencie permissões e exclua contas

### 🎨 **Interface**

- Design responsivo que funciona em qualquer dispositivo
- Componentes modernos e intuitivos
- Feedback visual para todas as ações
- Navegação simples e clara

## 🛠️ Tecnologias

- **Next.js 12** - Framework React
- **TypeScript** - Tipagem segura
- **Tailwind CSS** - Estilização moderna
- **React Hook Form** - Formulários eficientes
- **Axios** - Comunicação com API
- **Lucide React** - Ícones bonitos

## 🎯 Próximos passos

Estamos trabalhando em:

- Sistema de comentários
- Notificações em tempo real
- Modo escuro
- Dashboard personalizado
- Sistema de tags

---

**StudyBlog** - Transformando a forma como compartilhamos conhecimento! 📚✨
