import React from "react";
import { Head<PERSON> } from "@/components/organisms/Header";
import { Footer } from "@/components/organisms/Footer";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  );
}
